import { ConfirmationPageComponent } from './shared/components/confirmation-page/confirmation-page.component';
import { PaymentCallbackComponent } from './shared/components/payment-callback/payment-callback.component';
import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { AboutUsComponent } from './pages/about-us/about-us.component';
import { BecomeDriverComponent } from './pages/become-driver/become-driver.component';
import { FaqsComponent } from './pages/faqs/faqs.component';
import { ContactUsComponent } from './pages/contact-us/contact-us.component';
import { PrivacyAndPoliciesComponent } from './pages/privacy-and-policies/privacy-and-policies.component';
import { LoginRegistrationComponent } from './pages/login-registration/login-registration.component';
import { HomeComponent } from './pages/home/<USER>';
import { PickDropDetailsComponent } from './shared/components/pick-drop-details/pick-drop-details.component';
import { PaymentBookingDetailsComponent } from './shared/components/payment-booking-details/payment-booking-details.component';
import { UserprofileComponent } from './pages/management/userprofile/userprofile.component';
import { AuthGuard } from './shared/services';
import { RegistrationComponent } from './pages/registration/registration.component';
import { OtpVerificationComponent } from './pages/otp-verification/otp-verification.component';
import { UserRidesComponent } from './pages/management/user-rides/user-rides.component';
import { TermsAndConditionsComponent } from './pages/terms-and-conditions/terms-and-conditions.component';
import { RefundPolicyComponent } from './pages/refund-policy/refund-policy.component';

const routes: Routes =  [
  { path: '',   redirectTo: 'home', pathMatch: 'full' },
  { path: 'home',   component: HomeComponent },
  {
    path: 'userprofile',
    component: UserprofileComponent,
    canActivate: [AuthGuard],
    children: [
      {
        path: 'overview',
        component: UserRidesComponent
      },
      {
        path: 'booking-receipt/:id',
        component: ConfirmationPageComponent
      }, 
      { 
        path: '**', 
        redirectTo: 'overview', 
        pathMatch: 'full' 
      }
    ]
  },
  { path: 'pickupdropoffdetails/:travelcities', component: PickDropDetailsComponent },
  { 
    path: 'bookingpaymentdeatils', 
    component: PaymentBookingDetailsComponent, 
    canActivate: [AuthGuard] 
  },
  { path: 'aboutus', component: AboutUsComponent },
  { path: 'becomedriver', component: BecomeDriverComponent },
  { path: 'faqs', component: FaqsComponent },
  { path: 'contactus', component: ContactUsComponent },
  { path: 'privacy-policy', component: PrivacyAndPoliciesComponent },
  { path: 'terms-and-conditions', component: TermsAndConditionsComponent },
  { path: 'refund-policy', component: RefundPolicyComponent },
  { path: 'privacy&policy', component: PrivacyAndPoliciesComponent },
  { path: 'login', component: LoginRegistrationComponent },
  { path: 'signup', component: LoginRegistrationComponent },
  { path: 'register', component: RegistrationComponent},
  { path: 'registration', component: RegistrationComponent},
  { path: 'otp-verification', component: OtpVerificationComponent},
  { path: 'payment-callback', component: PaymentCallbackComponent },
  { path: '**', redirectTo: 'home', pathMatch: 'full' }
];

@NgModule({
  imports: [RouterModule.forRoot(routes, { useHash: true })],
  exports: [RouterModule]
})
export class AppRoutingModule { }
