<header id="header" class="loginpage d-lg-none col-lg-12">
    <div class="loginlogo">
        <a href="#" class="logo">
            <img src="{{imageFolderPath}}/logo.png" alt="CabYaari" />
        </a>
    </div>
    
    <div class="loginform">
        <div class="col-12 col-12-small">
            <form method="post" action="#">
                <div class="row gtr-50">
                    <div class="col-12">
                        <h3>Verify OTP</h3>
                        <p>We have sent an OTP to {{phoneNumber}}</p>
                        
                        <div class="inputrow">
                            <input type="text" name="otp" [(ngModel)]="otp" 
                                class="form-control" placeholder="Enter OTP" 
                                maxlength="6" required autofocus />
                        </div>

                        <div class="login" (click)="verifyOtp()">Verify OTP</div>
                        
                        <div class="bottombtn">
                            <a (click)="resendOtp()" [class.disabled]="isResending">
                                {{isResending ? 'Sending...' : 'Resend OTP'}}
                            </a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</header>

<div id="main" class="loginpage d-none d-lg-block col-lg-12">
    <section id="two" class="logincontent">
        <div class="container">
            <div class="row">
                <div class="col-lg-6 offset-lg-3">
                    <div class="loginform-desktop">
                        <div class="loginlogo-desktop">
                            <a href="#" class="logo">
                                <img src="{{imageFolderPath}}/logo.png" alt="CabYaari" />
                            </a>
                        </div>
                        
                        <form method="post" action="#">
                            <div class="row gtr-50">
                                <div class="col-12">
                                    <h3>Verify OTP</h3>
                                    <p>We have sent an OTP to {{phoneNumber}}</p>
                                    
                                    <div class="inputrow">
                                        <input type="text" name="otp" [(ngModel)]="otp" 
                                            class="form-control" placeholder="Enter OTP" 
                                            maxlength="6" required autofocus />
                                    </div>

                                    <div class="login" (click)="verifyOtp()">Verify OTP</div>
                                    
                                    <div class="bottombtn">
                                        <a (click)="resendOtp()" [class.disabled]="isResending">
                                            {{isResending ? 'Sending...' : 'Resend OTP'}}
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
