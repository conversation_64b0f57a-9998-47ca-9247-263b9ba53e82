import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { AuthService } from 'src/app/shared/services';
import { OtpVerificationRequest } from 'src/app/shared/models/otp-verification-request.model';
import { OtpVerificationResponse } from 'src/app/shared/models/otp-verification-response.model';
import { ProgressService } from 'src/app/shared/services/progress.service';
import { AppConstants } from 'src/app/shared/constants/AppConstants';
import { ValidationMessages, ErrorMessage, SuccessMessage } from 'src/app/shared/constants/message.content';
import { LoggerService } from 'src/app/shared/interceptors/logger.service';
import { AppConfig } from 'src/app/configs/app.config';

@Component({
  selector: 'app-otp-verification',
  templateUrl: './otp-verification.component.html',
  styleUrls: ['./otp-verification.component.css']
})
export class OtpVerificationComponent implements OnInit {

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private authService: AuthService,
    private toastrService: ToastrService,
    private progressService: ProgressService
  ) {
    this.logger = LoggerService.createLogger('OtpVerificationComponent');
  }

  private logger: LoggerService;

  phoneNumber: string = '';
  otp: string = '';
  verificationType: string = ''; // 'registration' or 'login'
  returnUrl: string = '';
  isResending: boolean = false;
  imageFolderPath: string = AppConfig.imageFolderPath;

  ngOnInit() {
    // Get parameters from query params
    const queryParams = this.route.snapshot.queryParams;
    this.phoneNumber = queryParams['phoneNumber'] || '';
    this.verificationType = queryParams['type'] || 'login';
    this.returnUrl = queryParams['returnUrl'] || '';

    console.log('OTP Verification initialized with:', {
      phoneNumber: this.phoneNumber,
      type: this.verificationType,
      returnUrl: this.returnUrl
    });

    if (!this.phoneNumber) {
      this.showToastrError('Error', 'Phone number is required for OTP verification');
      this.router.navigate(['/login']);
    }
  }

  verifyOtp() {
    if (!this.isValidOtp()) {
      return;
    }

    const otpRequest: OtpVerificationRequest = {
      phoneNumber: this.phoneNumber,
      otp: this.otp
    };

    this.showProgressBar();
    this.authService.verifyOtp(otpRequest).subscribe(
      (response: OtpVerificationResponse) => {
        this.hideProgressBar();
        console.log('OTP verification response', response);

        if (response.succeeded) {
          this.showToastrSuccess('Success', 'OTP verified successfully');
          
          // Navigate based on return URL or default to user profile
          let navigationUrl = this.returnUrl || '/userprofile';
          if (navigationUrl === '/userprofile') {
            this.router.navigate([navigationUrl]);
          } else {
            this.router.navigateByUrl(navigationUrl);
          }
        } else {
          if (response.message) {
            this.showToastrError('Verification Failed', response.message);
          } else {
            this.showToastrError('Verification Failed', 'Invalid OTP. Please try again.');
          }
        }
      },
      (error) => {
        this.hideProgressBar();
        console.log('OTP verification error', error);

        if (error && error.Message) {
          this.showToastrError('Verification Failed', error.Message);
        } else {
          this.showToastrError('Verification Failed', 'Unable to verify OTP. Please try again.');
        }
      }
    );
  }

  resendOtp() {
    if (this.isResending) {
      return;
    }

    this.isResending = true;
    
    // Determine which API to call based on verification type
    if (this.verificationType === 'registration') {
      // For registration, we would need to call register-phone again
      // This might not be ideal, but depends on backend implementation
      this.showToastrInfo('Info', 'Please go back to registration to resend OTP');
      this.isResending = false;
      return;
    } else {
      // For login, call login-phone API again
      this.authService.loginWithPhone({ phoneNumber: this.phoneNumber }).subscribe(
        (response) => {
          this.isResending = false;
          if (response.succeeded && response.data.otpSent) {
            this.showToastrSuccess('Success', 'OTP sent successfully');
          } else {
            this.showToastrError('Error', response.message || 'Failed to resend OTP');
          }
        },
        (error) => {
          this.isResending = false;
          this.showToastrError('Error', 'Failed to resend OTP. Please try again.');
        }
      );
    }
  }

  private isValidOtp(): boolean {
    if (!this.otp || this.otp.trim().length === 0) {
      this.showToastrError('Validation Error', 'Please enter OTP');
      return false;
    }

    if (this.otp.trim().length < 4 || this.otp.trim().length > 6) {
      this.showToastrError('Validation Error', 'OTP must be 4-6 digits');
      return false;
    }

    return true;
  }

  private showToastrError(title: string, message: string) {
    this.toastrService.error(message, title);
  }

  private showToastrSuccess(title: string, message: string) {
    this.toastrService.success(message, title);
  }

  private showToastrInfo(title: string, message: string) {
    this.toastrService.info(message, title);
  }

  private showProgressBar() {
    this.progressService.isPorgress.next(true);
  }

  private hideProgressBar() {
    this.progressService.isPorgress.next(false);
  }
}
